# 网页聊天室本地副本

## 项目说明
这是从 `https://test4.test9988.com` 完整复制的网页聊天室应用的本地版本。

## 文件结构
```
├── chatroom.html                           # 主HTML文件
├── h5/h5/static/                          # 静态资源目录
│   ├── css/                               # CSS样式文件
│   │   ├── photoswipe.css                 # PhotoSwipe主样式
│   │   └── default-skin/                  # PhotoSwipe皮肤
│   │       ├── default-skin.css           # 皮肤样式
│   │       ├── default-skin.png           # 皮肤图标(PNG)
│   │       ├── default-skin.svg           # 皮肤图标(SVG)
│   │       └── preloader.gif              # 加载动画
│   └── js/                                # JavaScript文件
│       ├── jquery-1.7.2.min.js           # jQuery库
│       ├── reconnecting-websocket.js      # WebSocket重连库
│       ├── photoswipe.min.js              # PhotoSwipe核心
│       ├── photoswipe-ui-default.min.js   # PhotoSwipe UI
│       └── pako.es5.min.js                # 压缩/解压库
└── README.md                              # 说明文档
```

## 技术特点
- **前端框架**: 基于jQuery的单页应用
- **实时通信**: WebSocket连接 (`wss://test4.test9988.com/chat`)
- **图片查看**: PhotoSwipe图片浏览器
- **数据压缩**: 使用Pako库进行数据压缩
- **代码混淆**: 主要业务逻辑经过压缩和编码处理

## 配置信息
- **API地址**: `https://test4.test9988.com/api/v1`
- **WebSocket地址**: `wss://test4.test9988.com/chat`
- **访问Token**: `6105305057a32efe151d8591afbc8480_7ba1fb511c8349c0bf7f6531415fb9a765b03d93`

## 使用方法
1. 直接在浏览器中打开 `chatroom.html` 文件
2. 或者使用本地HTTP服务器运行（推荐）:
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx http-server
   
   # 使用PHP
   php -S localhost:8000
   ```

## 注意事项
- 本地文件需要通过HTTP服务器访问才能正常工作（避免CORS问题）
- WebSocket连接仍然指向原服务器，需要网络连接
- Token可能有时效性，过期后需要重新获取

## 抓取时间
- 抓取日期: 2025年7月31日
- 原始URL: https://test4.test9988.com?token=6105305057a32efe151d8591afbc8480_7ba1fb511c8349c0bf7f6531415fb9a765b03d93
